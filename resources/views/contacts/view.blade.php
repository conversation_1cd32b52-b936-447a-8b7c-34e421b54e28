<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Google Contacts</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .contact-table th, .contact-table td {
            vertical-align: top;
            font-size: 0.9rem;
        }
        .no-data {
            color: #6c757d;
            font-style: italic;
        }
        .phone-cleaned {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .other-details {
            max-width: 200px;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title mb-0">
                            <i class="bi bi-person-lines-fill me-2"></i>
                            My Google Contacts
                        </h3>
                        <p class="mb-0 mt-2">Total Contacts: <strong>{{ $totalContacts }}</strong></p>
                    </div>
                    
                    <div class="card-body">
                        @if($totalContacts > 0)
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle me-2"></i>
                                Successfully imported {{ $totalContacts }} contacts from your Google account.
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-striped table-hover contact-table">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Sr. No</th>
                                            <th>Name</th>
                                            <th>Phone (Cleaned)</th>
                                            <th>Original Phone</th>
                                            <th>Email</th>
                                            <th>DOB</th>
                                            <th>Gender</th>
                                            <th>Address</th>
                                            <th>Other Details</th>
                                            <th>Imported At</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($contacts as $index => $contact)
                                            <tr>
                                                <td>{{ $index + 1 }}</td>
                                                <td>
                                                    @if($contact->name)
                                                        <strong>{{ $contact->name }}</strong>
                                                    @else
                                                        <span class="no-data">No name</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($contact->phone)
                                                        <span class="phone-cleaned">{{ $contact->phone }}</span>
                                                    @else
                                                        <span class="no-data">No phone</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($contact->original_phone)
                                                        {{ $contact->original_phone }}
                                                    @else
                                                        <span class="no-data">No original phone</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($contact->email)
                                                        <a href="mailto:{{ $contact->email }}">{{ $contact->email }}</a>
                                                    @else
                                                        <span class="no-data">No email</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($contact->date_of_birth)
                                                        {{ $contact->date_of_birth->format('Y-m-d') }}
                                                    @else
                                                        <span class="no-data">No DOB</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($contact->gender)
                                                        <span class="badge bg-secondary">{{ ucfirst($contact->gender) }}</span>
                                                    @else
                                                        <span class="no-data">No gender</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($contact->address)
                                                        <small>{{ Str::limit($contact->address, 50) }}</small>
                                                    @else
                                                        <span class="no-data">No address</span>
                                                    @endif
                                                </td>
                                                <td class="other-details">
                                                    @if($contact->other_details)
                                                        <small>{{ $contact->other_details_formatted }}</small>
                                                    @else
                                                        <span class="no-data">No other details</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <small>{{ $contact->created_at->format('Y-m-d H:i') }}</small>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">Storage Statistics</h6>
                                            <ul class="list-unstyled mb-0">
                                                <li><strong>Total Contacts:</strong> {{ $totalContacts }}</li>
                                                <li><strong>With Phone Numbers:</strong> {{ $contacts->whereNotNull('phone')->count() }}</li>
                                                <li><strong>With Email Addresses:</strong> {{ $contacts->whereNotNull('email')->count() }}</li>
                                                <li><strong>With Date of Birth:</strong> {{ $contacts->whereNotNull('date_of_birth')->count() }}</li>
                                                <li><strong>With Addresses:</strong> {{ $contacts->whereNotNull('address')->count() }}</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-info text-white">
                                        <div class="card-body">
                                            <h6 class="card-title">Phone Number Cleaning</h6>
                                            <p class="card-text mb-0">
                                                Phone numbers are automatically cleaned by removing all non-digit characters. 
                                                For example: "(************* (mobile)" becomes "9909458097".
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                No contacts found. Try importing your Google contacts by logging in with Google.
                            </div>
                            
                            <div class="text-center">
                                <a href="/auth/google/redirect" class="btn btn-primary">
                                    <i class="bi bi-google me-2"></i>
                                    Import Google Contacts
                                </a>
                            </div>
                        @endif
                        
                        <div class="mt-4 text-center">
                            <a href="/" class="btn btn-secondary">
                                <i class="bi bi-house me-2"></i>
                                Back to Home
                            </a>
                            @if($totalContacts > 0)
                                <a href="/auth/google/redirect" class="btn btn-outline-primary ms-2">
                                    <i class="bi bi-arrow-clockwise me-2"></i>
                                    Re-import Contacts
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
