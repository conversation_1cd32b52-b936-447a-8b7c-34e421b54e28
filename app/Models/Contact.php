<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Contact extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'email',
        'phone',
        'original_phone',
        'date_of_birth',
        'gender',
        'address',
        'other_details',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date_of_birth' => 'date',
        'other_details' => 'array',
    ];

    /**
     * Get the user that owns the contact.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Clean phone number by removing all non-digit characters.
     *
     * @param string $phoneNumber
     * @return string|null
     */
    public static function cleanPhoneNumber(?string $phoneNumber): ?string
    {
        if (empty($phoneNumber)) {
            return null;
        }

        // Remove all non-digit characters
        $cleaned = preg_replace('/\D/', '', $phoneNumber);

        // Return null if no digits found
        return empty($cleaned) ? null : $cleaned;
    }

    /**
     * Create or update contact for a user, handling duplicates.
     *
     * @param int $userId
     * @param array $contactData
     * @return Contact
     */
    public static function createOrUpdateForUser(int $userId, array $contactData): Contact
    {
        $cleanedPhone = self::cleanPhoneNumber($contactData['original_phone'] ?? null);

        // Prepare the data
        $data = [
            'user_id' => $userId,
            'name' => $contactData['name'] ?? null,
            'email' => $contactData['email'] ?? null,
            'phone' => $cleanedPhone,
            'original_phone' => $contactData['original_phone'] ?? null,
            'date_of_birth' => $contactData['date_of_birth'] ?? null,
            'gender' => $contactData['gender'] ?? null,
            'address' => $contactData['address'] ?? null,
            'other_details' => $contactData['other_details'] ?? null,
        ];

        // Try to find existing contact by phone or email
        $existingContact = null;

        if ($cleanedPhone) {
            $existingContact = self::where('user_id', $userId)
                ->where('phone', $cleanedPhone)
                ->first();
        }

        if (!$existingContact && !empty($data['email'])) {
            $existingContact = self::where('user_id', $userId)
                ->where('email', $data['email'])
                ->first();
        }

        if ($existingContact) {
            // Update existing contact
            $existingContact->update($data);
            return $existingContact;
        } else {
            // Create new contact
            return self::create($data);
        }
    }

    /**
     * Get formatted phone number for display.
     *
     * @return string
     */
    public function getFormattedPhoneAttribute(): string
    {
        return $this->original_phone ?: $this->phone ?: 'No phone';
    }

    /**
     * Get contact's full details as formatted string.
     *
     * @return string
     */
    public function getOtherDetailsFormattedAttribute(): string
    {
        if (empty($this->other_details)) {
            return 'No additional details';
        }

        $details = [];
        foreach ($this->other_details as $key => $value) {
            if (is_array($value)) {
                $details[] = ucfirst($key) . ': ' . implode(', ', $value);
            } else {
                $details[] = ucfirst($key) . ': ' . $value;
            }
        }

        return implode(' | ', $details);
    }
}
