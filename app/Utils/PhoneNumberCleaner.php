<?php

namespace App\Utils;

class PhoneNumberCleaner
{
    /**
     * Clean phone number by removing all non-digit characters.
     * 
     * Examples:
     * - "(************* (mobile)" becomes "9909458097"
     * - "******-123-4567" becomes "15551234567"
     * - "************ ext 123" becomes "5551234567"
     *
     * @param string|null $phoneNumber
     * @return string|null
     */
    public static function clean(?string $phoneNumber): ?string
    {
        if (empty($phoneNumber)) {
            return null;
        }

        // Remove all non-digit characters
        $cleaned = preg_replace('/\D/', '', $phoneNumber);
        
        // Return null if no digits found
        return empty($cleaned) ? null : $cleaned;
    }

    /**
     * Format phone number for display (keep original formatting).
     *
     * @param string|null $originalPhone
     * @param string|null $cleanedPhone
     * @return string
     */
    public static function formatForDisplay(?string $originalPhone, ?string $cleanedPhone): string
    {
        if (!empty($originalPhone)) {
            return $originalPhone;
        }
        
        if (!empty($cleanedPhone)) {
            return $cleanedPhone;
        }
        
        return 'No phone';
    }

    /**
     * Validate if phone number has minimum required digits.
     *
     * @param string|null $phoneNumber
     * @param int $minDigits
     * @return bool
     */
    public static function isValid(?string $phoneNumber, int $minDigits = 10): bool
    {
        $cleaned = self::clean($phoneNumber);
        return !empty($cleaned) && strlen($cleaned) >= $minDigits;
    }

    /**
     * Extract phone type from original phone string.
     * 
     * Examples:
     * - "(************* (mobile)" returns "mobile"
     * - "555-1234 work" returns "work"
     * - "555-1234" returns null
     *
     * @param string|null $originalPhone
     * @return string|null
     */
    public static function extractType(?string $originalPhone): ?string
    {
        if (empty($originalPhone)) {
            return null;
        }

        // Look for common phone type patterns
        $patterns = [
            '/\(mobile\)/i' => 'mobile',
            '/\(work\)/i' => 'work',
            '/\(home\)/i' => 'home',
            '/\(cell\)/i' => 'mobile',
            '/\(office\)/i' => 'work',
            '/mobile/i' => 'mobile',
            '/work/i' => 'work',
            '/home/<USER>' => 'home',
            '/cell/i' => 'mobile',
            '/office/i' => 'work',
        ];

        foreach ($patterns as $pattern => $type) {
            if (preg_match($pattern, $originalPhone)) {
                return $type;
            }
        }

        return null;
    }

    /**
     * Clean and validate multiple phone numbers.
     *
     * @param array $phoneNumbers Array of phone number strings
     * @return array Array of cleaned phone numbers with metadata
     */
    public static function cleanMultiple(array $phoneNumbers): array
    {
        $result = [];
        
        foreach ($phoneNumbers as $phone) {
            $cleaned = self::clean($phone);
            if (!empty($cleaned)) {
                $result[] = [
                    'original' => $phone,
                    'cleaned' => $cleaned,
                    'type' => self::extractType($phone),
                    'is_valid' => self::isValid($phone)
                ];
            }
        }
        
        return $result;
    }
}
