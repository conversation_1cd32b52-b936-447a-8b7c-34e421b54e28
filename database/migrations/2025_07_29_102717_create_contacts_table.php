<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contacts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone', 20)->nullable(); // Cleaned phone number (digits only)
            $table->string('original_phone')->nullable(); // Original phone as received from Google
            $table->date('date_of_birth')->nullable();
            $table->string('gender', 20)->nullable();
            $table->text('address')->nullable();
            $table->json('other_details')->nullable(); // For organizations, notes, etc.
            $table->timestamps();

            // Indexes for better performance
            $table->index(['user_id', 'phone']);
            $table->index(['user_id', 'email']);

            // Unique constraint to prevent duplicate contacts for same user
            $table->unique(['user_id', 'phone'], 'unique_user_phone');
            $table->unique(['user_id', 'email'], 'unique_user_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contacts');
    }
};
