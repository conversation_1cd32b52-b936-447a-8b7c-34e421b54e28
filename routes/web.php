<?php

use App\Http\Controllers\Auth\GoogleLoginController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\MeetingAddressController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\StaticPageController;
use App\Http\Controllers\UserGalleryController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Http\Request;

// Root route - handles both guests and authenticated users
Route::get('/', function () {
    if (auth()->check()) {
        // For authenticated users, check profile completion
        $user = auth()->user();
        if (!$user->isProfileComplete()) {
            return redirect()->route('profile.edit')
                ->with('warning', 'Please complete your profile to continue. Your profile is ' . $user->getProfileCompletionPercentage() . '% complete.');
        }
        // Profile is complete, show home dashboard
        return app(HomeController::class)->index();
    } else {
        // For guests, show landing page
        return view('landing');
    }
})->name('home');

// Redirect /home to root for backward compatibility
Route::get('/home', function () {
    return redirect('/');
});







// Site mode pages (accessible even when middleware is active)
Route::get('/maintenance', function () {
    return view('site-modes.maintenance');
})->name('maintenance');

Route::get('/coming-soon', function () {
    return view('site-modes.coming-soon');
})->name('coming-soon');



// Admin authentication routes (public)
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/login', [App\Http\Controllers\Admin\AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [App\Http\Controllers\Admin\AuthController::class, 'login']);
    Route::post('/logout', [App\Http\Controllers\Admin\AuthController::class, 'logout'])->name('logout');
});

Route::middleware('auth')->group(function () {
    // Profile routes (no middleware needed - users need access to complete profile)
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::patch('/profile/privacy-settings', [ProfileController::class, 'updatePrivacySettings'])->name('profile.privacy-settings.update');
    Route::patch('/profile/time-spending', [ProfileController::class, 'updateTimeSpending'])->name('profile.time-spending.update');


    Route::patch('/profile/couple-activity', [ProfileController::class, 'updateCoupleActivity'])->name('profile.couple-activity.update');
    Route::patch('/profile/sugar-partner', [ProfileController::class, 'updateSugarPartner'])->name('profile.sugar-partner.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Notification preferences routes
    Route::get('/profile/notifications', [App\Http\Controllers\UserNotificationPreferenceController::class, 'show'])->name('profile.notifications.show');
    Route::patch('/profile/notifications', [App\Http\Controllers\UserNotificationPreferenceController::class, 'update'])->name('profile.notifications.update');
    Route::post('/profile/notifications/reset', [App\Http\Controllers\UserNotificationPreferenceController::class, 'resetAjax'])->name('profile.notifications.reset');

    // Gallery routes (no middleware needed - part of profile completion)
    Route::post('/gallery/upload', [UserGalleryController::class, 'upload'])->name('gallery.upload');
    Route::post('/gallery/reorder', [UserGalleryController::class, 'reorder'])->name('gallery.reorder');
    Route::delete('/gallery/{id}', [UserGalleryController::class, 'delete'])->name('gallery.delete');



    // Special route for viewing profiles from hire requests (bypasses profile completion requirement)
    Route::get('/find-person/{user}/from-hire-request', [App\Http\Controllers\FindPersonController::class, 'showFromHireRequest'])->name('find-person.show-from-hire-request');

    // Notification routes (accessible to all authenticated users regardless of profile completion)
    Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications/{notification}/mark-as-read', [NotificationController::class, 'markAsRead'])->name('notifications.mark-as-read');
    Route::post('/notifications/mark-all-as-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-as-read');
    Route::get('/notifications/unread-count', [NotificationController::class, 'getUnreadCount'])->name('notifications.unread-count');





    // Routes that require complete profile
    Route::middleware('profile.complete')->group(function () {
        // Find Person routes
        Route::get('/find-person', [App\Http\Controllers\FindPersonController::class, 'index'])->name('find-person.index');
        Route::get('/find-person/{user}', [App\Http\Controllers\FindPersonController::class, 'show'])->name('find-person.show');

        // Couple Activity routes
        Route::get('/couple-activity', [App\Http\Controllers\CoupleActivityController::class, 'index'])->name('couple-activity.index');
        Route::get('/couple-activity/select-partner', [App\Http\Controllers\CoupleActivityController::class, 'selectPartner'])->name('couple-activity.select-partner');
        Route::get('/couple-activity/couple-event', [App\Http\Controllers\CoupleActivityController::class, 'coupleEvent'])->name('couple-activity.couple-event');
        Route::get('/couple-activity/search-users', [App\Http\Controllers\CoupleActivityController::class, 'searchUsers'])->name('couple-activity.search-users');
        Route::post('/couple-activity/send-request', [App\Http\Controllers\CoupleActivityController::class, 'sendRequest'])->name('couple-activity.send-request');
        Route::post('/couple-activity/approve/{coupleActivityRequest}', [App\Http\Controllers\CoupleActivityController::class, 'approveRequest'])->name('couple-activity.approve');
        Route::post('/couple-activity/reject/{coupleActivityRequest}', [App\Http\Controllers\CoupleActivityController::class, 'rejectRequest'])->name('couple-activity.reject');
        Route::post('/couple-activity/block/{coupleActivityRequest}', [App\Http\Controllers\CoupleActivityController::class, 'blockRequest'])->name('couple-activity.block');
        Route::post('/couple-activity/unblock', [App\Http\Controllers\CoupleActivityController::class, 'unblockUser'])->name('couple-activity.unblock');
        Route::delete('/couple-activity/cancel/{coupleActivityRequest}', [App\Http\Controllers\CoupleActivityController::class, 'cancelRequest'])->name('couple-activity.cancel-request');
        Route::post('/couple-activity/end-partnership', [App\Http\Controllers\CoupleActivityController::class, 'endPartnership'])->name('couple-activity.end-partnership');

        // Event address routes
        Route::get('/event-address', [MeetingAddressController::class, 'index'])->name('event.address');
        Route::get('/event-address/{id}', [MeetingAddressController::class, 'show'])->name('event.address.show');
        Route::get('/event-join-now/{id}', [MeetingAddressController::class, 'joinNow'])->name('event.join-now');

        // Transaction routes (redirect to wallet)
        Route::get('/transactions', function() { return redirect()->route('wallet.index'); });
        Route::get('/payment', [PaymentController::class, 'index'])->name('payment.index'); // Keep for backward compatibility
        Route::post('/payment/process', [PaymentController::class, 'process'])->name('payment.process');
        Route::get('/payment/meeting-event/{eventId}', [PaymentController::class, 'meetingEventPayment'])->name('payment.meeting-event');
        Route::post('/payment/meeting-event/process', [PaymentController::class, 'processMeetingEventPayment'])->name('payment.meeting-event.process');
        Route::post('/simulate-event-payment', [PaymentController::class, 'simulateEventPayment'])->name('simulate.event.payment');
        Route::get('/simulate-payment', [PaymentController::class, 'simulatePayment'])->name('simulate.payment');

        // Time Spending Booking routes
        Route::get('/booking/available-slots/{provider}', [App\Http\Controllers\BookingController::class, 'getAvailableSlots'])->name('booking.available-slots');
        Route::post('/booking/create', [App\Http\Controllers\BookingController::class, 'store'])->name('booking.store');
        Route::get('/booking/{booking}/payment', [App\Http\Controllers\BookingController::class, 'getBookingForPayment'])->name('booking.payment');
        Route::post('/booking/process-payment', [App\Http\Controllers\BookingController::class, 'processPayment'])->name('booking.process-payment');
        Route::post('/booking/{booking}/cancel', [App\Http\Controllers\BookingController::class, 'cancelBooking'])->name('booking.cancel');

        // Booking update difference payment routes
        Route::get('/booking/{booking}/difference-payment', [App\Http\Controllers\BookingController::class, 'getDifferencePaymentDetails'])->name('booking.difference-payment');
        Route::post('/booking/process-difference-payment', [App\Http\Controllers\BookingController::class, 'processDifferencePayment'])->name('booking.process-difference-payment');
        Route::post('/booking/{booking}/cancel-pending', [App\Http\Controllers\BookingController::class, 'cancelPendingBooking'])->name('booking.cancel-pending');
        Route::put('/booking/{booking}/update', [App\Http\Controllers\BookingController::class, 'updateBooking'])->name('booking.update');
        Route::get('/booking/user-bookings/{provider}', [App\Http\Controllers\BookingController::class, 'getUserBookingsForProvider'])->name('booking.user-bookings');
        Route::get('/booking/{bookingId}/chat-details', [App\Http\Controllers\BookingController::class, 'getChatDetails'])->name('booking.chat-details');
        Route::get('/booking/{bookingId}/details', [App\Http\Controllers\BookingController::class, 'getBookingDetails'])->name('booking.details');
        Route::get('/booking/status/{providerId}', [App\Http\Controllers\BookingController::class, 'getBookingStatus'])->name('booking.status');

        // Meeting verification routes
        Route::post('/meeting-verification/{bookingId}/start-photo', [App\Http\Controllers\MeetingVerificationController::class, 'uploadStartPhoto'])->name('meeting.verification.start-photo');
        Route::post('/meeting-verification/{bookingId}/end-photo', [App\Http\Controllers\MeetingVerificationController::class, 'uploadEndPhoto'])->name('meeting.verification.end-photo');
        Route::get('/meeting-verification/{bookingId}/status', [App\Http\Controllers\MeetingVerificationController::class, 'getVerificationStatus'])->name('meeting.verification.status');

        // Rating & Review routes
        Route::get('/rating/{bookingId}', [App\Http\Controllers\RatingReviewController::class, 'showRatingForm'])->name('rating.show');
        Route::post('/rating/submit', [App\Http\Controllers\RatingReviewController::class, 'submitRating'])->name('rating.submit');
        Route::get('/reviews/user/{userId}', [App\Http\Controllers\RatingReviewController::class, 'getUserReviews'])->name('reviews.user');
        Route::get('/reviews/can-review/{bookingId}', [App\Http\Controllers\RatingReviewController::class, 'canReview'])->name('reviews.can-review');
        Route::get('/reviews/pending', [App\Http\Controllers\RatingReviewController::class, 'getPendingReviews'])->name('reviews.pending');

        // Post-meeting action routes
        Route::get('/post-meeting/{bookingId}/status', [App\Http\Controllers\PostMeetingActionController::class, 'getActionStatus'])->name('post-meeting.status');
        Route::post('/post-meeting/report-no-show', [App\Http\Controllers\PostMeetingActionController::class, 'reportNoShow'])->name('post-meeting.report-no-show');
        Route::post('/post-meeting/end-meeting', [App\Http\Controllers\PostMeetingActionController::class, 'endMeeting'])->name('post-meeting.end-meeting');

        // Bookings routes for user detail page
        Route::get('/bookings/user/{userId}', [App\Http\Controllers\BookingController::class, 'getUserBookings'])->name('bookings.user');

        // Dispute routes
        Route::prefix('dispute')->name('dispute.')->group(function () {
            Route::post('/raise', [App\Http\Controllers\DisputeController::class, 'raiseDispute'])->name('raise');
            Route::get('/details/{booking}', [App\Http\Controllers\DisputeController::class, 'getDisputeDetails'])->name('details');
            Route::get('/my-disputes', [App\Http\Controllers\DisputeController::class, 'getUserDisputes'])->name('my-disputes');
            Route::get('/history', [App\Http\Controllers\DisputeController::class, 'disputeHistory'])->name('history');
        });

        // Wallet routes
        Route::prefix('wallet')->name('wallet.')->group(function () {
            Route::get('/', [App\Http\Controllers\WalletController::class, 'index'])->name('index');
            Route::post('/add-bank-account', [App\Http\Controllers\WalletController::class, 'addBankAccount'])->name('add-bank-account');
            Route::put('/update-bank-account/{account}', [App\Http\Controllers\WalletController::class, 'updateBankAccount'])->name('update-bank-account');
            Route::delete('/delete-bank-account/{account}', [App\Http\Controllers\WalletController::class, 'deleteBankAccount'])->name('delete-bank-account');
            Route::post('/request-withdrawal', [App\Http\Controllers\WalletController::class, 'requestWithdrawal'])->name('request-withdrawal');
            Route::post('/cancel-withdrawal/{withdrawal}', [App\Http\Controllers\WalletController::class, 'cancelWithdrawal'])->name('cancel-withdrawal');
            Route::get('/settings', [App\Http\Controllers\WalletController::class, 'getWithdrawalSettings'])->name('settings');
        });

        // Calendar Booking Management routes
        Route::get('/calendar', [App\Http\Controllers\CalendarController::class, 'index'])->name('calendar.index');
        Route::get('/calendar/bookings/{date}', [App\Http\Controllers\CalendarController::class, 'getBookingsForDate'])->name('calendar.bookings');

        // Provider Booking Management routes (require active subscription)
        Route::middleware('subscription.required')->group(function () {
            Route::get('/provider/booking-requests', [App\Http\Controllers\ProviderBookingController::class, 'getBookingRequests'])->name('provider.booking-requests');
            Route::post('/provider/booking/{booking}/accept', [App\Http\Controllers\ProviderBookingController::class, 'acceptBooking'])->name('provider.booking.accept');
            Route::post('/provider/booking/{booking}/reject', [App\Http\Controllers\ProviderBookingController::class, 'rejectBooking'])->name('provider.booking.reject');
            Route::post('/provider/booking/{booking}/block', [App\Http\Controllers\ProviderBookingController::class, 'blockClient'])->name('provider.booking.block');
            Route::post('/provider/user/{user}/unblock', [App\Http\Controllers\ProviderBookingController::class, 'unblockClient'])->name('provider.user.unblock');
        });

        // Chat routes (protected with chat access validation)
        Route::middleware('chat.access')->group(function () {
            Route::get('/chat', [App\Http\Controllers\ChatController::class, 'index'])->name('chat.index');
            Route::get('/chat/{user}', [App\Http\Controllers\ChatController::class, 'chatWithUser'])->name('chat.user');
        });
        Route::get('/chat/active', [App\Http\Controllers\ChatController::class, 'getActiveChats'])->name('chat.active');
        Route::get('/chat/unread-count', [App\Http\Controllers\ChatController::class, 'getUnreadCount'])->name('chat.unread-count');
        Route::get('/chat/{booking}/messages', [App\Http\Controllers\ChatController::class, 'getMessages'])->name('chat.messages');
        Route::post('/chat/{booking}/send', [App\Http\Controllers\ChatController::class, 'sendMessage'])->name('chat.send');

        // Broadcasting auth
        Route::post('/broadcasting/auth', function (Request $request) {
            return Broadcast::auth($request);
        });

        // Legacy wallet API routes
        Route::get('/wallet/api', [App\Http\Controllers\WalletController::class, 'getWallet'])->name('wallet.api');
        Route::get('/wallet/transactions', [App\Http\Controllers\WalletController::class, 'getTransactions'])->name('wallet.transactions');
        Route::get('/wallet/balance', [App\Http\Controllers\WalletController::class, 'getBalance'])->name('wallet.balance');
        Route::post('/wallet/check-affordability', [App\Http\Controllers\WalletController::class, 'checkAffordability'])->name('wallet.check-affordability');
        Route::post('/wallet/use-for-booking', [App\Http\Controllers\WalletController::class, 'useWalletForBooking'])->name('wallet.use-for-booking');

        // User availability route
        Route::get('/user/{user}/availability', [App\Http\Controllers\UserController::class, 'getAvailability'])->name('user.availability');





        // Sugar Partner Exchange routes
        Route::prefix('sugar-partner/exchange')->name('sugar-partner.exchange.')->group(function () {
            // Route::get('/{exchange}/payment', [App\Http\Controllers\SugarPartnerExchangeController::class, 'showPayment'])->name('payment'); // Removed - integrated into profile tab
            Route::post('/{exchange}/create-order', [App\Http\Controllers\SugarPartnerExchangeController::class, 'createOrder'])->name('create-order');
            Route::post('/{exchange}/payment', [App\Http\Controllers\SugarPartnerExchangeController::class, 'processPayment'])->name('payment');
            Route::get('/{exchange}/status', [App\Http\Controllers\SugarPartnerExchangeController::class, 'showStatus'])->name('status');
            Route::get('/{exchange}/profile/{user}', [App\Http\Controllers\SugarPartnerExchangeController::class, 'showProfile'])->name('profile');
            Route::post('/{exchange}/response', [App\Http\Controllers\SugarPartnerExchangeController::class, 'submitResponse'])->name('submit-response');
        });

        // Sugar Partner Hard Reject Management routes
        Route::prefix('sugar-partner/hard-rejects')->name('sugar-partner.hard-rejects.')->group(function () {
            Route::get('/', [App\Http\Controllers\SugarPartnerHardRejectController::class, 'index'])->name('index');
            Route::post('/convert-to-soft', [App\Http\Controllers\SugarPartnerHardRejectController::class, 'convertToSoftReject'])->name('convert-to-soft');
            Route::post('/convert-to-hard', [App\Http\Controllers\SugarPartnerHardRejectController::class, 'convertToHardReject'])->name('convert-to-hard');
            Route::post('/remove-completely', [App\Http\Controllers\SugarPartnerHardRejectController::class, 'removeCompletely'])->name('remove-completely');
            Route::get('/details', [App\Http\Controllers\SugarPartnerHardRejectController::class, 'getDetails'])->name('details');
        });

        // Sugar Partner Partnership Management routes
        Route::prefix('sugar-partner/partnerships')->name('sugar-partner.partnerships.')->group(function () {
            Route::get('/', [App\Http\Controllers\SugarPartnerPartnershipController::class, 'index'])->name('index');
            Route::get('/{partnership}', [App\Http\Controllers\SugarPartnerPartnershipController::class, 'show'])->name('show');
            Route::delete('/{partnership}/end', [App\Http\Controllers\SugarPartnerPartnershipController::class, 'destroy'])->name('destroy');
        });

        // User subscription routes
        Route::prefix('subscription')->name('subscription.')->group(function () {
            Route::get('/plans', [App\Http\Controllers\UserSubscriptionController::class, 'getPlans'])->name('plans');
            Route::get('/status', [App\Http\Controllers\UserSubscriptionController::class, 'getStatus'])->name('status');
            Route::post('/purchase', [App\Http\Controllers\UserSubscriptionController::class, 'purchase'])->name('purchase');
            Route::post('/process-payment', [App\Http\Controllers\UserSubscriptionController::class, 'processPayment'])->name('process-payment');
        });

        // User Report routes
        Route::post('/report-user', [App\Http\Controllers\ReportController::class, 'store'])->name('report.store');

    });
});

// Admin routes (protected)
Route::middleware('admin')->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [App\Http\Controllers\Admin\AdminController::class, 'index'])->name('dashboard');
    Route::get('/users', [App\Http\Controllers\Admin\AdminController::class, 'users'])->name('users');
    Route::get('/management-users', [App\Http\Controllers\Admin\AdminController::class, 'managementUsers'])->name('management-users');
    Route::post('/users/{user}/toggle-admin', [App\Http\Controllers\Admin\AdminController::class, 'toggleAdmin'])->name('users.toggle-admin');

    // Admin Sugar Partners routes
    Route::prefix('sugar-partners')->name('sugar-partners.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\SugarPartnerController::class, 'index'])->name('index');
        Route::get('/data', [App\Http\Controllers\Admin\SugarPartnerController::class, 'getData'])->name('data');
        Route::get('/{user}/profile', [App\Http\Controllers\Admin\SugarPartnerController::class, 'show'])->name('profile');

        // Exchange management routes
        Route::post('/initiate-exchange', [App\Http\Controllers\Admin\SugarPartnerExchangeController::class, 'initiateExchange'])->name('initiate-exchange');
        Route::get('/exchanges', [App\Http\Controllers\Admin\SugarPartnerExchangeController::class, 'index'])->name('exchanges.index');
        Route::get('/exchanges/data', [App\Http\Controllers\Admin\SugarPartnerExchangeController::class, 'getData'])->name('exchanges.data');
        Route::get('/exchanges/{exchange}', [App\Http\Controllers\Admin\SugarPartnerExchangeController::class, 'show'])->name('exchanges.show');
        Route::post('/exchanges/{exchange}/cancel', [App\Http\Controllers\Admin\SugarPartnerExchangeController::class, 'cancel'])->name('exchanges.cancel');
    });

    // Admin settings routes
    Route::get('/settings', [App\Http\Controllers\Admin\SettingController::class, 'index'])->name('settings.index');
    Route::post('/settings', [App\Http\Controllers\Admin\SettingController::class, 'update'])->name('settings.update');

    // Admin contact management routes
    Route::prefix('contact')->name('contact.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\ContactController::class, 'index'])->name('index');
        Route::get('/data', [App\Http\Controllers\Admin\ContactController::class, 'getData'])->name('data');
        Route::get('/stats', [App\Http\Controllers\Admin\ContactController::class, 'getStats'])->name('stats');
        Route::get('/{id}', [App\Http\Controllers\Admin\ContactController::class, 'show'])->name('show');
        Route::put('/{id}', [App\Http\Controllers\Admin\ContactController::class, 'update'])->name('update');
        Route::delete('/{id}', [App\Http\Controllers\Admin\ContactController::class, 'destroy'])->name('destroy');
    });
    Route::post('/settings/upload-file', [App\Http\Controllers\Admin\SettingController::class, 'uploadFile'])->name('settings.upload-file');
    Route::post('/settings/test-razorpay', [App\Http\Controllers\Admin\SettingController::class, 'testRazorpay'])->name('settings.test-razorpay');
    Route::post('/settings/clean-database', [App\Http\Controllers\Admin\SettingController::class, 'cleanDatabase'])->name('settings.clean-database');
    Route::post('/settings/export-database', [App\Http\Controllers\Admin\SettingController::class, 'exportDatabase'])->name('settings.export-database');

    // Admin notification routes
    Route::get('/notifications', [App\Http\Controllers\Admin\NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications', [App\Http\Controllers\Admin\NotificationController::class, 'store'])->name('notifications.store');
    Route::delete('/notifications/{notification}', [App\Http\Controllers\Admin\NotificationController::class, 'destroy'])->name('notifications.destroy');

    // Admin meeting events routes
    Route::get('/meeting-events', [App\Http\Controllers\Admin\MeetingEventController::class, 'index'])->name('meeting-events.index');
    Route::get('/meeting-events/create', [App\Http\Controllers\Admin\MeetingEventController::class, 'create'])->name('meeting-events.create');
    Route::post('/meeting-events', [App\Http\Controllers\Admin\MeetingEventController::class, 'store'])->name('meeting-events.store');
    Route::get('/meeting-events/{meetingEvent}/edit', [App\Http\Controllers\Admin\MeetingEventController::class, 'edit'])->name('meeting-events.edit');
    Route::put('/meeting-events/{meetingEvent}', [App\Http\Controllers\Admin\MeetingEventController::class, 'update'])->name('meeting-events.update');
    Route::delete('/meeting-events/{meetingEvent}', [App\Http\Controllers\Admin\MeetingEventController::class, 'destroy'])->name('meeting-events.destroy');
    Route::patch('/meeting-events/{meetingEvent}/toggle-status', [App\Http\Controllers\Admin\MeetingEventController::class, 'toggleStatus'])->name('meeting-events.toggle-status');

    // Admin features routes
    Route::get('/features', [App\Http\Controllers\Admin\FeatureController::class, 'index'])->name('features.index');
    Route::post('/features/{feature}/status', [App\Http\Controllers\Admin\FeatureController::class, 'updateStatus'])->name('features.update-status');
    Route::post('/features/{feature}/sugar-partner-pricing', [App\Http\Controllers\Admin\FeatureController::class, 'updateSugarPartnerPricing'])->name('features.update-sugar-partner-pricing');

    // Admin revenue management routes
    Route::prefix('revenue')->name('revenue.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\RevenueController::class, 'index'])->name('index');
        Route::get('/stats', [App\Http\Controllers\Admin\RevenueController::class, 'getStats'])->name('stats');
        Route::get('/records', [App\Http\Controllers\Admin\RevenueController::class, 'getRevenues'])->name('records');
        Route::get('/export', [App\Http\Controllers\Admin\RevenueController::class, 'export'])->name('export');
    });

    // Admin analytics data routes (integrated into main dashboard)
    Route::get('/analytics-data', [App\Http\Controllers\Admin\AdminController::class, 'getAnalyticsData'])->name('analytics.data');
    Route::get('/analytics-export', [App\Http\Controllers\Admin\AdminController::class, 'exportAnalytics'])->name('analytics.export');

    // Backward compatibility routes for old analytics endpoints
    Route::get('/analytics/data', function(\Illuminate\Http\Request $request) {
        return redirect('/admin/analytics-data?' . $request->getQueryString());
    });
    Route::get('/analytics/export', function(\Illuminate\Http\Request $request) {
        return redirect('/admin/analytics-export?' . $request->getQueryString());
    });

    // Admin escrow management routes
    Route::prefix('escrow')->name('escrow.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\EscrowController::class, 'index'])->name('index');
        Route::post('/release/{booking}', [App\Http\Controllers\Admin\EscrowController::class, 'releasePayment'])->name('release');
        Route::post('/refund/{booking}', [App\Http\Controllers\Admin\EscrowController::class, 'refundPayment'])->name('refund');
        Route::get('/booking/{booking}', [App\Http\Controllers\Admin\EscrowController::class, 'getBookingDetails'])->name('booking.details');

        // Dispute management routes
        Route::post('/dispute/{booking}/update-status', [App\Http\Controllers\Admin\EscrowController::class, 'updateDisputeStatus'])->name('dispute.update-status');
        Route::post('/dispute/{booking}/resolve-client', [App\Http\Controllers\Admin\EscrowController::class, 'resolveForClient'])->name('dispute.resolve-client');
        Route::post('/dispute/{booking}/resolve-provider', [App\Http\Controllers\Admin\EscrowController::class, 'resolveForProvider'])->name('dispute.resolve-provider');
    });

    // Admin withdrawal management routes
    Route::prefix('withdrawals')->name('withdrawals.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\WithdrawalController::class, 'index'])->name('index');
        Route::get('/{withdrawal}', [App\Http\Controllers\Admin\WithdrawalController::class, 'show'])->name('show');
        Route::get('/{withdrawal}/details', [App\Http\Controllers\Admin\WithdrawalController::class, 'getDetails'])->name('details');
        Route::post('/{withdrawal}/process', [App\Http\Controllers\Admin\WithdrawalController::class, 'process'])->name('process');
        Route::post('/{withdrawal}/reject', [App\Http\Controllers\Admin\WithdrawalController::class, 'reject'])->name('reject');
        Route::post('/{withdrawal}/update-status', [App\Http\Controllers\Admin\WithdrawalController::class, 'updateStatus'])->name('update-status');
        Route::get('/stats/data', [App\Http\Controllers\Admin\WithdrawalController::class, 'getStats'])->name('stats');
        Route::get('/export/csv', [App\Http\Controllers\Admin\WithdrawalController::class, 'export'])->name('export');
    });

    // AJAX API routes for dynamic functionality
    Route::prefix('api')->name('api.')->group(function () {
        // Dashboard API
        Route::get('/dashboard-stats', [App\Http\Controllers\Admin\AdminController::class, 'getDashboardStats'])->name('dashboard.stats');

        // Users API
        Route::get('/users/data', [App\Http\Controllers\Admin\AdminController::class, 'getUsersData'])->name('users.data');
        Route::get('/users/{user}/details', [App\Http\Controllers\Admin\AdminController::class, 'getUserDetails'])->name('users.details');
        Route::post('/users/{user}/suspend', [App\Http\Controllers\Admin\AdminController::class, 'suspendUser'])->name('users.suspend');
        Route::post('/users/{user}/activate', [App\Http\Controllers\Admin\AdminController::class, 'activateUser'])->name('users.activate');

        // Management Users API
        Route::get('/management-users/data', [App\Http\Controllers\Admin\AdminController::class, 'getManagementUsersData'])->name('management-users.data');

        // Meeting Events API
        Route::get('/meeting-events/data', [App\Http\Controllers\Admin\MeetingEventController::class, 'getEventsData'])->name('meeting-events.data');
        Route::post('/meeting-events/{meetingEvent}/toggle-status-ajax', [App\Http\Controllers\Admin\MeetingEventController::class, 'toggleStatusAjax'])->name('meeting-events.toggle-status-ajax');
        Route::delete('/meeting-events/{meetingEvent}/ajax', [App\Http\Controllers\Admin\MeetingEventController::class, 'destroyAjax'])->name('meeting-events.destroy-ajax');

        // Notifications API
        Route::get('/notifications/data', [App\Http\Controllers\Admin\NotificationController::class, 'getNotificationsData'])->name('notifications.data');
        Route::post('/notifications/ajax', [App\Http\Controllers\Admin\NotificationController::class, 'storeAjax'])->name('notifications.store-ajax');
        Route::delete('/notifications/{notification}/ajax', [App\Http\Controllers\Admin\NotificationController::class, 'destroyAjax'])->name('notifications.destroy-ajax');
        Route::post('/notifications/test-firebase', [App\Http\Controllers\Admin\NotificationController::class, 'testFirebase'])->name('notifications.test-firebase');
        Route::get('/notifications/statistics', [App\Http\Controllers\Admin\NotificationController::class, 'getStatistics'])->name('notifications.statistics');

        // User search API for notifications
        Route::get('/users/search', [App\Http\Controllers\Admin\NotificationController::class, 'searchUsers'])->name('users.search');
        Route::get('/users/{user}', [App\Http\Controllers\Admin\NotificationController::class, 'getUser'])->name('users.get');

        // Settings API
        Route::post('/settings/ajax', [App\Http\Controllers\Admin\SettingController::class, 'updateAjax'])->name('settings.update-ajax');
        Route::get('/site-mode-status', [App\Http\Controllers\Admin\SettingController::class, 'getSiteModeStatus'])->name('site-mode-status');
    });

    // Subscription Plans
    Route::get('/subscription-plans/data', [App\Http\Controllers\Admin\SubscriptionPlanController::class, 'getData'])->name('subscription-plans.data');
    Route::post('/subscription-plans/{subscriptionPlan}/toggle-status', [App\Http\Controllers\Admin\SubscriptionPlanController::class, 'toggleStatus'])->name('subscription-plans.toggle-status');
    Route::resource('subscription-plans', App\Http\Controllers\Admin\SubscriptionPlanController::class);

    // Admin Reports routes
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\ReportController::class, 'index'])->name('index');
        Route::get('/data', [App\Http\Controllers\Admin\ReportController::class, 'getData'])->name('data');
        Route::get('/debug', function() {
            $reports = App\Models\UserReport::with(['reporter', 'reportedUser'])->get();
            return response()->json([
                'total_reports' => $reports->count(),
                'reports' => $reports->map(function($report) {
                    return [
                        'id' => $report->id,
                        'reporter' => $report->reporter ? $report->reporter->name : 'Unknown',
                        'reported_user' => $report->reportedUser ? $report->reportedUser->name : 'Unknown',
                        'category' => $report->category,
                        'status' => $report->status,
                        'created_at' => $report->created_at->format('Y-m-d H:i:s')
                    ];
                })
            ]);
        })->name('debug');
        Route::get('/{report}', [App\Http\Controllers\Admin\ReportController::class, 'show'])->name('show');
        Route::post('/{report}/suspend-user', [App\Http\Controllers\Admin\ReportController::class, 'suspendUser'])->name('suspend-user');
        Route::post('/{report}/send-warning', [App\Http\Controllers\Admin\ReportController::class, 'sendWarning'])->name('send-warning');
        Route::post('/{report}/dismiss', [App\Http\Controllers\Admin\ReportController::class, 'dismissReport'])->name('dismiss');
        Route::post('/{report}/update-status', [App\Http\Controllers\Admin\ReportController::class, 'updateStatus'])->name('update-status');

        // Decision modification routes
        Route::post('/{report}/change-status', [App\Http\Controllers\Admin\ReportController::class, 'changeStatus'])->name('change-status');
        Route::post('/{report}/lift-suspension', [App\Http\Controllers\Admin\ReportController::class, 'liftSuspension'])->name('lift-suspension');
        Route::post('/{report}/reverse-decision', [App\Http\Controllers\Admin\ReportController::class, 'reverseDecision'])->name('reverse-decision');
    });

});

// Temporary testing routes for reports (remove in production)
Route::get('/test-reports', function() {
    $reports = App\Models\UserReport::with(['reporter', 'reportedUser'])->get();
    return view('admin.reports.test', compact('reports'));
});

Route::get('/test-reports-data', function() {
    $reports = App\Models\UserReport::with(['reporter', 'reportedUser'])->get();

    $data = [
        'draw' => 1,
        'recordsTotal' => $reports->count(),
        'recordsFiltered' => $reports->count(),
        'data' => $reports->map(function($report) {
            return [
                'id' => $report->id,
                'reporter' => $report->reporter ? $report->reporter->name : 'Unknown',
                'reported_user' => $report->reportedUser ? $report->reportedUser->name : 'Unknown',
                'category' => $report->getCategoryDisplayName(),
                'status' => '<span class="badge bg-warning">' . ucfirst(str_replace('_', ' ', $report->status)) . '</span>',
                'created_at' => $report->created_at->format('M d, Y H:i'),
                'actions' => '<div class="btn-group"><button class="btn btn-sm btn-outline-primary">View</button></div>'
            ];
        })->toArray()
    ];

    return response()->json($data);
});

// Temporary admin login for testing (remove in production)
Route::get('/test-admin-login', function() {
    $admin = App\Models\User::where('email', '<EMAIL>')->first();
    if ($admin) {
        auth()->login($admin);
        return redirect('/admin/reports')->with('success', 'Logged in as admin for testing');
    }
    return 'Admin user not found';
});

// Direct test of DataTables data (remove in production)
Route::get('/test-datatables-direct', function() {
    try {
        $admin = App\Models\User::where('email', '<EMAIL>')->first();
        if ($admin) {
            auth()->login($admin);
        }

        $reports = App\Models\UserReport::with(['reporter', 'reportedUser'])->get();

        $data = [];
        foreach($reports as $report) {
            $data[] = [
                'id' => $report->id,
                'reporter' => $report->reporter ? $report->reporter->name : 'Unknown',
                'reported_user' => $report->reportedUser ? $report->reportedUser->name : 'Unknown',
                'category' => $report->getCategoryDisplayName(),
                'status' => '<span class="badge bg-warning">' . ucfirst(str_replace('_', ' ', $report->status)) . '</span>',
                'created_at' => $report->created_at->format('M d, Y H:i'),
                'actions' => '<div class="btn-group"><button class="btn btn-sm btn-outline-primary">View</button></div>'
            ];
        }

        return response()->json([
            'draw' => 1,
            'recordsTotal' => count($data),
            'recordsFiltered' => count($data),
            'data' => $data
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

// Test suspend user functionality (remove in production)
Route::post('/test-suspend-user/{reportId}', function($reportId) {
    try {
        $admin = App\Models\User::where('email', '<EMAIL>')->first();
        if ($admin) {
            auth()->login($admin);
        }

        $report = App\Models\UserReport::findOrFail($reportId);
        $controller = new App\Http\Controllers\Admin\ReportController(new App\Services\FirebaseService());

        $request = new Illuminate\Http\Request([
            'duration' => '24h',
            'reason' => 'Test suspension from debug route'
        ]);

        return $controller->suspendUser($request, $report);

    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

require __DIR__.'/auth.php';



// Static pages routes (accessible to guests, require complete profile for authenticated users)
Route::middleware('profile.complete')->group(function () {
    Route::get('/about-us', [StaticPageController::class, 'aboutUs'])->name('static.about-us');
    Route::get('/how-it-works', [StaticPageController::class, 'howItWorks'])->name('static.how-it-works');
    Route::get('/safety-tips', [StaticPageController::class, 'safetyTips'])->name('static.safety-tips');
    Route::get('/help-center', [StaticPageController::class, 'helpCenter'])->name('static.help-center');
    Route::get('/contact-us', [StaticPageController::class, 'contactUs'])->name('static.contact-us');
    Route::get('/success-stories', [StaticPageController::class, 'successStories'])->name('static.success-stories');

    // Contact form submission route
    Route::post('/contact', [App\Http\Controllers\ContactController::class, 'store'])->name('contact.store');
    Route::get('/privacy-policy', [StaticPageController::class, 'privacyPolicy'])->name('static.privacy-policy');
    Route::get('/terms-of-service', [StaticPageController::class, 'termsOfService'])->name('static.terms-of-service');
    Route::get('/refund-policy', [StaticPageController::class, 'refundPolicy'])->name('static.refund-policy');
});

// Google OAuth routes
Route::get('/auth/google/redirect', [GoogleLoginController::class, 'redirectToGoogle'])->name('google.login');
Route::get('/auth/callback/google', [GoogleLoginController::class, 'handleGoogleCallback']);

// Clear OAuth session route (for debugging)
Route::get('/auth/google/clear', function() {
    session()->flush();
    return redirect('/auth/google/redirect')->with('message', 'Session cleared. Trying Google OAuth again...');
})->name('google.clear');

// View stored contacts route (for verification)
Route::get('/contacts/view', function() {
    if (!auth()->check()) {
        return redirect('/login')->with('error', 'Please login to view contacts.');
    }

    $contacts = \App\Models\Contact::where('user_id', auth()->id())->get();
    $totalContacts = $contacts->count();

    return view('contacts.view', compact('contacts', 'totalContacts'));
})->name('contacts.view');

// Gallery test route (no auth required) - moved to API routes to avoid CSRF
Route::get('/api/gallery/test', function() {
    return response()->json([
        'success' => true,
        'php_limits' => [
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'max_file_uploads' => ini_get('max_file_uploads'),
            'memory_limit' => ini_get('memory_limit')
        ]
    ]);
});





// Data cleanup route (admin only)
Route::get('/admin/data-cleanup', function () {
    // Check if user is admin
    if (!auth()->check() || !auth()->user()->isAdmin()) {
        abort(403, 'Unauthorized access');
    }

    return view('admin.data-cleanup');
})->name('admin.data.cleanup');

Route::post('/admin/data-cleanup/execute', function () {
    // Check if user is admin
    if (!auth()->check() || !auth()->user()->isAdmin()) {
        return response()->json(['success' => false, 'message' => 'Unauthorized access'], 403);
    }

    try {
        // Run the cleanup command
        \Illuminate\Support\Facades\Artisan::call('data:cleanup-db', ['--force' => true]);

        return response()->json([
            'success' => true,
            'message' => 'Data cleanup completed successfully! All user data has been cleaned while preserving subscription plans and features.'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error during cleanup: ' . $e->getMessage()
        ], 500);
    }
})->name('admin.data.cleanup.execute');

// SEO Routes
Route::get('/sitemap.xml', [App\Http\Controllers\SitemapController::class, 'index'])->name('sitemap');

// PWA Routes
Route::get('/manifest.json', function () {
    $mobileIcon = App\Models\Setting::get('mobile_icon');
    $themeColor = App\Models\Setting::get('theme_color', '#C9B6E4');
    $backgroundColor = App\Models\Setting::get('background_color', '#ffffff');
    $iconUrl = $mobileIcon ? url('storage/' . $mobileIcon) : url('images/icon-512x512.png');

    $manifest = [
        'name' => config('app.name', 'SettingWala') . '',
        'short_name' => config('app.name', 'SettingWala'),
        'description' => 'Connect with like-minded individuals and discover meaningful relationships through verified profiles and local events.',
        'start_url' => '/',
        'display' => 'standalone',
        'background_color' => $backgroundColor,
        'theme_color' => $themeColor,
        'orientation' => 'portrait-primary',
        'scope' => '/',
        'lang' => 'en',
        'categories' => ['social', 'lifestyle'],
        'icons' => [],
    ];

    // If mobile icon is uploaded, use it for all sizes
    if ($mobileIcon) {
        $sizes = ['72x72', '96x96', '128x128', '144x144', '152x152', '192x192', '384x384', '512x512'];
        foreach ($sizes as $size) {
            $manifest['icons'][] = [
                'src' => url('storage/' . $mobileIcon),
                'sizes' => $size,
                'type' => 'image/png',
                'purpose' => 'any maskable'
            ];
        }
    } else {
        // Use default static icons
        $manifest['icons'] = [
            [
                'src' => asset('images/icon-72x72.png'),
                'sizes' => '72x72',
                'type' => 'image/png',
                'purpose' => 'any maskable'
            ],
            [
                'src' => asset('images/icon-96x96.png'),
                'sizes' => '96x96',
                'type' => 'image/png',
                'purpose' => 'any maskable'
            ],
            [
                'src' => asset('images/icon-128x128.png'),
                'sizes' => '128x128',
                'type' => 'image/png',
                'purpose' => 'any maskable'
            ],
            [
                'src' => asset('images/icon-144x144.png'),
                'sizes' => '144x144',
                'type' => 'image/png',
                'purpose' => 'any'
            ],
            [
                'src' => asset('images/icon-152x152.png'),
                'sizes' => '152x152',
                'type' => 'image/png',
                'purpose' => 'any maskable'
            ],
            [
                'src' => asset('images/icon-192x192.png'),
                'sizes' => '192x192',
                'type' => 'image/png',
                'purpose' => 'any maskable'
            ],
            [
                'src' => asset('images/icon-384x384.png'),
                'sizes' => '384x384',
                'type' => 'image/png',
                'purpose' => 'any maskable'
            ],
            [
                'src' => asset('images/icon-512x512.png'),
                'sizes' => '512x512',
                'type' => 'image/png',
                'purpose' => 'any maskable'
            ]
        ];
    }

    $manifest['screenshots'] = [
        [
            'src' => asset('images/screenshot-narrow.png'),
            'sizes' => '540x720',
            'type' => 'image/png',
            'form_factor' => 'narrow'
        ],
        [
            'src' => asset('images/screenshot-wide.png'),
            'sizes' => '1280x720',
            'type' => 'image/png',
            'form_factor' => 'wide'
        ]
    ];

    $manifest['prefer_related_applications'] = false;

    return response()->json($manifest, 200, [
        'Content-Type' => 'application/json',
        'Cache-Control' => 'no-cache, no-store, must-revalidate',
        'Pragma' => 'no-cache',
        'Expires' => '0'
    ]);
});

Route::get('/sw.js', function () {
    return response()->file(public_path('sw.js'), [
        'Content-Type' => 'application/javascript'
    ]);
});

// PWA Features API
Route::get('/api/pwa/features', function () {
    $enabledFeatures = [
        'meeting_events' => \App\Models\Feature::isEnabled('meeting_events'),
        'time_spending' => \App\Models\Feature::isEnabled('time_spending'),
        'subscription_model' => \App\Models\Feature::isEnabled('subscription_model'),
        'chat_system' => \App\Models\Feature::isEnabled('chat_system'),
        'partner_swapping' => \App\Models\Feature::isEnabled('partner_swapping'),
        'notifications' => \App\Models\Feature::isEnabled('notifications'),
        'gallery' => \App\Models\Feature::isEnabled('gallery'),
        'rating_review_system' => \App\Models\Feature::isEnabled('rating_review_system'),
        'sugar_partner' => \App\Models\Feature::isEnabled('sugar_partner'),
        'user_verification' => \App\Models\Feature::isEnabled('user_verification'),
        'location_services' => \App\Models\Feature::isEnabled('location_services'),
        'privacy_controls' => \App\Models\Feature::isEnabled('privacy_controls'),
    ];

    return response()->json([
        'features' => $enabledFeatures,
        'app_name' => config('app.name', 'SettingWala')
    ]);
});

// Firebase Configuration API
Route::get('/api/firebase-config', function () {
    $projectId = \App\Models\Setting::get('firebase_project_id');

    if (empty($projectId)) {
        return response()->json(['error' => 'Firebase not configured'], 404);
    }

    return response()->json([
        'apiKey' => \App\Models\Setting::get('firebase_web_api_key', 'test-web-api-key'),
        'authDomain' => $projectId . '.firebaseapp.com',
        'projectId' => $projectId,
        'storageBucket' => $projectId . '.appspot.com',
        'messagingSenderId' => \App\Models\Setting::get('firebase_messaging_sender_id', 'test-sender-id'),
        'appId' => \App\Models\Setting::get('firebase_app_id', 'test-app-id'),
        'vapidKey' => \App\Models\Setting::get('firebase_vapid_key', 'test-vapid-key')
    ]);
});

// FCM Token API
Route::middleware('auth')->post('/api/fcm-token', function (\Illuminate\Http\Request $request) {
    $request->validate([
        'token' => 'required|string'
    ]);

    $user = auth()->user();
    $user->update(['fcm_token' => $request->token]);

    return response()->json(['success' => true]);
});

// Notification Permission Tracking API
Route::middleware('auth')->prefix('api/notification-permission')->group(function () {
    Route::get('/can-request', [App\Http\Controllers\NotificationPermissionController::class, 'canRequest']);
    Route::post('/record-request', [App\Http\Controllers\NotificationPermissionController::class, 'recordRequest']);
    Route::post('/record-result', [App\Http\Controllers\NotificationPermissionController::class, 'recordResult']);
    Route::post('/mark-logged-in', [App\Http\Controllers\NotificationPermissionController::class, 'markLoggedInBefore']);
    Route::get('/status', [App\Http\Controllers\NotificationPermissionController::class, 'getStatus']);
    Route::post('/clear-tracking', [App\Http\Controllers\NotificationPermissionController::class, 'clearTracking']);
});

// Comprehensive Notification System API
Route::middleware('auth')->prefix('api/notifications')->group(function () {
    // Notification management
    Route::get('/', [App\Http\Controllers\Api\NotificationController::class, 'index']);
    Route::get('/unread-count', [App\Http\Controllers\Api\NotificationController::class, 'unreadCount']);
    Route::get('/stats', [App\Http\Controllers\Api\NotificationController::class, 'stats']);
    Route::get('/category/{category}', [App\Http\Controllers\Api\NotificationController::class, 'byCategory']);

    // Notification actions
    Route::post('/{notificationId}/read', [App\Http\Controllers\Api\NotificationController::class, 'markAsRead']);
    Route::post('/mark-all-read', [App\Http\Controllers\Api\NotificationController::class, 'markAllAsRead']);
    Route::delete('/clear-all', [App\Http\Controllers\Api\NotificationController::class, 'clearAll']);

    // Push subscription management
    Route::post('/push-subscriptions', [App\Http\Controllers\Api\NotificationController::class, 'storePushSubscription']);
    Route::delete('/push-subscriptions', [App\Http\Controllers\Api\NotificationController::class, 'removePushSubscription']);

    // Notification preferences
    Route::get('/preferences', [App\Http\Controllers\Api\NotificationController::class, 'getPreferences']);
    Route::put('/preferences', [App\Http\Controllers\Api\NotificationController::class, 'updatePreferences']);


});


